%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 16dddab11d0a3934d85db2ba705badf7, type: 3}
  m_Name: NormalAttack
  m_EditorClassIdentifier: 
  adjustDistance: 3
  m_serializableDataList:
  - typeInfo:
      name: DramaPlayAnimationData
      fullName: Phoenix.Drama.DramaPlayAnimationData
      assemblyName: HotFix
    jsonData: "{\n    \"startFrame\": 0.0,\n    \"animationName\": \"Combat_AttackRootMotion\",\n   
      \"skillEffectId\": -1\n}"
  - typeInfo:
      name: DramaPlaySoundData
      fullName: Phoenix.Drama.DramaPlaySoundData
      assemblyName: HotFix
    jsonData: "{\n    \"startFrame\": 0.0,\n    \"name\": \"\",\n    \"wwiseEventReference\":
      {\n        \"path\": \"Assets/Res/Wwise/ScriptableObjects/Event/21607766-25AC-4DEA-99BC-81A0ECC2B36E.asset\",\n       
      \"guid\": \"bb7a5f2f4cbc5934686ceddc432dee9c\",\n        \"subName\": \"\"\n   
      },\n    \"skillEffectId\": -1\n}"
  - typeInfo:
      name: DramaPlayEffectData
      fullName: Phoenix.Drama.DramaPlayEffectData
      assemblyName: HotFix
    jsonData: "{\n    \"startFrame\": 10.0,\n    \"attachType\": {\n        \"m_enumName\":
      \"SourceEntity\",\n        \"value\": 1\n    },\n    \"skillEffectId\": 0,\n   
      \"bindPointId\": {\n        \"m_enumName\": \"None\",\n        \"value\": 0\n   
      },\n    \"efx\": {\n        \"path\": \"Assets/Res/Entity/Actor/jianxiake/Fx/PlayEFx_0_Hero2000_Combat_AttackRootMotion.prefab\",\n       
      \"guid\": \"374da7f46c0a4344c853ff4f0531bd16\",\n        \"subName\": \"\"\n   
      },\n    \"isFollow\": false,\n    \"useAttachScale\": false,\n    \"waitEnd\":
      false\n}"
  - typeInfo:
      name: DramaOnHitData
      fullName: Phoenix.Drama.DramaOnHitData
      assemblyName: HotFix
    jsonData: "{\n    \"startFrame\": 15.0,\n    \"skillEffectId\": 0,\n    \"effectType\":
      {\n        \"m_enumName\": \"Damage\",\n        \"value\": 3\n    },\n    \"damageEntityData\":
      {\n        \"moveLength\": 0.5,\n        \"damagePercent\": 15.0,\n       
      \"audioWeaponType\": {\n            \"m_enumName\": \"\",\n            \"value\":
      1\n        },\n        \"audioAttackType\": {\n            \"m_enumName\":
      \"\",\n            \"value\": 3\n        },\n        \"audioDamageLevel\":
      {\n            \"m_enumName\": \"\",\n            \"value\": 1\n        }\n   
      },\n    \"moveEntityData\": {\n        \"moveTime\": 0.0\n    },\n    \"entityExtraMoveData\":
      {},\n    \"entityExtraActionData\": {},\n    \"changeTeamEnergyData\": {},\n   
      \"changeTeamData\": {},\n    \"reduceCoolTimeData\": {}\n}"
  - typeInfo:
      name: DramaPlayEffectData
      fullName: Phoenix.Drama.DramaPlayEffectData
      assemblyName: HotFix
    jsonData: "{\n    \"startFrame\": 32.0,\n    \"attachType\": {\n        \"m_enumName\":
      \"SourceEntity\",\n        \"value\": 1\n    },\n    \"skillEffectId\": 0,\n   
      \"bindPointId\": {\n        \"m_enumName\": \"None\",\n        \"value\": 0\n   
      },\n    \"efx\": {\n        \"path\": \"Assets/Res/Entity/Actor/jianxiake/Fx/PlayEFx_1_Hero2000_Combat_AttackRootMotion.prefab\",\n       
      \"guid\": \"0220909a652dd6a418af5b4a54e4d870\",\n        \"subName\": \"\"\n   
      },\n    \"isFollow\": false,\n    \"useAttachScale\": false,\n    \"waitEnd\":
      false\n}"
  - typeInfo:
      name: DramaOnHitData
      fullName: Phoenix.Drama.DramaOnHitData
      assemblyName: HotFix
    jsonData: "{\n    \"startFrame\": 39.0,\n    \"skillEffectId\": 0,\n    \"effectType\":
      {\n        \"m_enumName\": \"Damage\",\n        \"value\": 3\n    },\n    \"damageEntityData\":
      {\n        \"moveLength\": 0.5,\n        \"damagePercent\": 15.0,\n       
      \"audioWeaponType\": {\n            \"m_enumName\": \"\",\n            \"value\":
      1\n        },\n        \"audioAttackType\": {\n            \"m_enumName\":
      \"\",\n            \"value\": 2\n        },\n        \"audioDamageLevel\":
      {\n            \"m_enumName\": \"\",\n            \"value\": 1\n        }\n   
      },\n    \"moveEntityData\": {\n        \"moveTime\": 0.0\n    },\n    \"entityExtraMoveData\":
      {},\n    \"entityExtraActionData\": {},\n    \"changeTeamEnergyData\": {},\n   
      \"changeTeamData\": {},\n    \"reduceCoolTimeData\": {}\n}"
  - typeInfo:
      name: DramaOnHitData
      fullName: Phoenix.Drama.DramaOnHitData
      assemblyName: HotFix
    jsonData: "{\n    \"startFrame\": 45.0,\n    \"skillEffectId\": 0,\n    \"effectType\":
      {\n        \"m_enumName\": \"Damage\",\n        \"value\": 3\n    },\n    \"damageEntityData\":
      {\n        \"moveLength\": 1.5,\n        \"damagePercent\": 20.0,\n       
      \"audioWeaponType\": {\n            \"m_enumName\": \"\",\n            \"value\":
      1\n        },\n        \"audioAttackType\": {\n            \"m_enumName\":
      \"\",\n            \"value\": 2\n        },\n        \"audioDamageLevel\":
      {\n            \"m_enumName\": \"\",\n            \"value\": 2\n        }\n   
      },\n    \"moveEntityData\": {\n        \"moveTime\": 0.0\n    },\n    \"entityExtraMoveData\":
      {},\n    \"entityExtraActionData\": {},\n    \"changeTeamEnergyData\": {},\n   
      \"changeTeamData\": {},\n    \"reduceCoolTimeData\": {}\n}"
  - typeInfo:
      name: DramaPlayEffectData
      fullName: Phoenix.Drama.DramaPlayEffectData
      assemblyName: HotFix
    jsonData: "{\n    \"startFrame\": 67.0,\n    \"attachType\": {\n        \"m_enumName\":
      \"SourceEntity\",\n        \"value\": 1\n    },\n    \"skillEffectId\": 0,\n   
      \"bindPointId\": {\n        \"m_enumName\": \"None\",\n        \"value\": 0\n   
      },\n    \"efx\": {\n        \"path\": \"Assets/Res/Entity/Actor/jianxiake/Fx/PlayEFx_2_Hero2000_Combat_AttackRootMotion.prefab\",\n       
      \"guid\": \"769d36b377d8fc64fb3c982c56feb668\",\n        \"subName\": \"\"\n   
      },\n    \"isFollow\": false,\n    \"useAttachScale\": false,\n    \"waitEnd\":
      false\n}"
  - typeInfo:
      name: DramaOnHitData
      fullName: Phoenix.Drama.DramaOnHitData
      assemblyName: HotFix
    jsonData: "{\n    \"startFrame\": 69.0,\n    \"skillEffectId\": 0,\n    \"effectType\":
      {\n        \"m_enumName\": \"Damage\",\n        \"value\": 3\n    },\n    \"damageEntityData\":
      {\n        \"moveLength\": 3.0,\n        \"damagePercent\": 20.0,\n       
      \"audioWeaponType\": {\n            \"m_enumName\": \"\",\n            \"value\":
      8\n        },\n        \"audioAttackType\": {\n            \"m_enumName\":
      \"\",\n            \"value\": 5\n        },\n        \"audioDamageLevel\":
      {\n            \"m_enumName\": \"\",\n            \"value\": 2\n        }\n   
      },\n    \"moveEntityData\": {\n        \"moveTime\": 0.0\n    },\n    \"entityExtraMoveData\":
      {},\n    \"entityExtraActionData\": {},\n    \"changeTeamEnergyData\": {},\n   
      \"changeTeamData\": {},\n    \"reduceCoolTimeData\": {}\n}"
  - typeInfo:
      name: DramaPlayEffectData
      fullName: Phoenix.Drama.DramaPlayEffectData
      assemblyName: HotFix
    jsonData: "{\n    \"startFrame\": 89.0,\n    \"attachType\": {\n        \"m_enumName\":
      \"SourceEntity\",\n        \"value\": 1\n    },\n    \"skillEffectId\": 0,\n   
      \"bindPointId\": {\n        \"m_enumName\": \"None\",\n        \"value\": 0\n   
      },\n    \"efx\": {\n        \"path\": \"Assets/Res/Entity/Actor/jianxiake/Fx/PlayEFx_3_Hero2000_Combat_AttackRootMotion.prefab\",\n       
      \"guid\": \"6540f8b570c4df347b094fa6568d9fe4\",\n        \"subName\": \"\"\n   
      },\n    \"isFollow\": false,\n    \"useAttachScale\": false,\n    \"waitEnd\":
      false\n}"
  - typeInfo:
      name: DramaOnHitData
      fullName: Phoenix.Drama.DramaOnHitData
      assemblyName: HotFix
    jsonData: "{\n    \"startFrame\": 93.0,\n    \"skillEffectId\": 0,\n    \"effectType\":
      {\n        \"m_enumName\": \"Damage\",\n        \"value\": 3\n    },\n    \"damageEntityData\":
      {\n        \"moveLength\": 2.0,\n        \"damagePercent\": 30.0,\n       
      \"audioWeaponType\": {\n            \"m_enumName\": \"\",\n            \"value\":
      1\n        },\n        \"audioAttackType\": {\n            \"m_enumName\":
      \"\",\n            \"value\": 2\n        },\n        \"audioDamageLevel\":
      {\n            \"m_enumName\": \"\",\n            \"value\": 3\n        }\n   
      },\n    \"moveEntityData\": {\n        \"moveTime\": 0.0\n    },\n    \"entityExtraMoveData\":
      {},\n    \"entityExtraActionData\": {},\n    \"changeTeamEnergyData\": {},\n   
      \"changeTeamData\": {},\n    \"reduceCoolTimeData\": {}\n}"
