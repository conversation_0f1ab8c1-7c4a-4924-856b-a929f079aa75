{"name": "AK.Wwise.Editor", "rootNamespace": "", "references": ["AK.Wwise", "Unity.Timeline", "Unity.Timeline.Editor", "Unity.Cinemachine", "Cinemachine", "HotFix", "GameCore", "HotfixEditor", "ConfigData", "Battle", "<PERSON><PERSON><PERSON>", "BaseCore", "BattleExecuter", "Common", "DotNetty", "Framework.Common", "MessagePack", "MessagePack.Annotations", "ParadoxNotion", "QFramework", "StompyRobot.SRDebugger", "StompyRobot.SRF", "UniTask", "Unity.RenderPipelines.Universal.Runtime", "YooAsset", "YooAsset.Editor"], "includePlatforms": ["Editor"], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [{"name": "com.unity.cinemachine", "expression": "[2.0.0,2.99.99]", "define": "CINEMACHINE_2"}, {"name": "com.unity.cinemachine", "expression": "[3.0.0,3.99.99]", "define": "CINEMACHINE_3"}], "noEngineReferences": false}