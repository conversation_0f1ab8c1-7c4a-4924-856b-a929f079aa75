%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 16dddab11d0a3934d85db2ba705badf7, type: 3}
  m_Name: Skill95
  m_EditorClassIdentifier: 
  adjustDistance: 3
  m_serializableDataList:
  - typeInfo:
      name: DramaPlayAnimationData
      fullName: Phoenix.Drama.DramaPlayAnimationData
      assemblyName: HotFix
    jsonData: "{\n    \"startFrame\": 0.0,\n    \"animationName\": \"Combat_ChaseHitAnnounce\",\n   
      \"skillEffectId\": -1\n}"
  - typeInfo:
      name: DramaPlaySoundData
      fullName: Phoenix.Drama.DramaPlaySoundData
      assemblyName: HotFix
    jsonData: "{\n    \"startFrame\": 0.0,\n    \"name\": \"\",\n    \"wwiseEventReference\":
      {\n        \"path\": \"Assets/Res/Wwise/ScriptableObjects/Event/C9D997C4-62B6-4442-A53C-1949795C626C.asset\",\n       
      \"guid\": \"fe42f7e8165677248b63c31178ea05ad\",\n        \"subName\": \"\"\n   
      },\n    \"skillEffectId\": -1\n}"
  - typeInfo:
      name: DramaPlayUnityTimelineData
      fullName: Phoenix.Drama.DramaPlayUnityTimelineData
      assemblyName: HotFix
    jsonData: "{\n    \"startFrame\": 0.0,\n    \"timeline\": {\n        \"path\":
      \"Assets/Res/Entity/Actor/jianxiake/Timeline/T01.prefab\",\n        \"guid\":
      \"67ebb22eb69e0e54984aeb5e1a3bee11\",\n        \"subName\": \"\"\n    },\n   
      \"attachType\": {\n        \"m_enumName\": \"SourceEntity\",\n        \"value\":
      1\n    }\n}"
  - typeInfo:
      name: DramaPlayAnimationData
      fullName: Phoenix.Drama.DramaPlayAnimationData
      assemblyName: HotFix
    jsonData: "{\n    \"startFrame\": 14.0,\n    \"animationName\": \"Combat_DoubleHitAnnounce\",\n   
      \"skillEffectId\": -1\n}"
  - typeInfo:
      name: DramaPlayAnimationData
      fullName: Phoenix.Drama.DramaPlayAnimationData
      assemblyName: HotFix
    jsonData: "{\n    \"startFrame\": 34.0,\n    \"animationName\": \"Combat_DoubleHitRootMotion\",\n   
      \"skillEffectId\": -1\n}"
  - typeInfo:
      name: DramaPlayEffectData
      fullName: Phoenix.Drama.DramaPlayEffectData
      assemblyName: HotFix
    jsonData: "{\n    \"startFrame\": 34.0,\n    \"attachType\": {\n        \"m_enumName\":
      \"SourceEntity\",\n        \"value\": 1\n    },\n    \"skillEffectId\": 0,\n   
      \"bindPointId\": {\n        \"m_enumName\": \"None\",\n        \"value\": 0\n   
      },\n    \"efx\": {\n        \"path\": \"Assets/Res/Entity/Actor/jianxiake/Fx/playEFx_0_Hero2000_Combat_DoubleHitRootMotion.prefab\",\n       
      \"guid\": \"074464faf0a96b64b9bb2c0230976794\",\n        \"subName\": \"\"\n   
      },\n    \"isFollow\": false,\n    \"useAttachScale\": false,\n    \"waitEnd\":
      false\n}"
  - typeInfo:
      name: DramaOnHitData
      fullName: Phoenix.Drama.DramaOnHitData
      assemblyName: HotFix
    jsonData: "{\n    \"startFrame\": 39.0,\n    \"skillEffectId\": 0,\n    \"effectType\":
      {\n        \"m_enumName\": \"Damage\",\n        \"value\": 3\n    },\n    \"damageEntityData\":
      {\n        \"moveLength\": 1.0,\n        \"damagePercent\": 20.0,\n       
      \"audioWeaponType\": {\n            \"m_enumName\": \"\",\n            \"value\":
      1\n        },\n        \"audioAttackType\": {\n            \"m_enumName\":
      \"\",\n            \"value\": 3\n        },\n        \"audioDamageLevel\":
      {\n            \"m_enumName\": \"\",\n            \"value\": 1\n        }\n   
      },\n    \"moveEntityData\": {\n        \"moveTime\": 0.0\n    },\n    \"entityExtraMoveData\":
      {},\n    \"entityExtraActionData\": {},\n    \"changeTeamEnergyData\": {},\n   
      \"changeTeamData\": {},\n    \"reduceCoolTimeData\": {}\n}"
  - typeInfo:
      name: DramaOnHitData
      fullName: Phoenix.Drama.DramaOnHitData
      assemblyName: HotFix
    jsonData: "{\n    \"startFrame\": 42.0,\n    \"skillEffectId\": 0,\n    \"effectType\":
      {\n        \"m_enumName\": \"Damage\",\n        \"value\": 3\n    },\n    \"damageEntityData\":
      {\n        \"moveLength\": 1.0,\n        \"damagePercent\": 20.0,\n       
      \"audioWeaponType\": {\n            \"m_enumName\": \"\",\n            \"value\":
      1\n        },\n        \"audioAttackType\": {\n            \"m_enumName\":
      \"\",\n            \"value\": 3\n        },\n        \"audioDamageLevel\":
      {\n            \"m_enumName\": \"\",\n            \"value\": 2\n        }\n   
      },\n    \"moveEntityData\": {\n        \"moveTime\": 0.0\n    },\n    \"entityExtraMoveData\":
      {},\n    \"entityExtraActionData\": {},\n    \"changeTeamEnergyData\": {},\n   
      \"changeTeamData\": {},\n    \"reduceCoolTimeData\": {}\n}"
  - typeInfo:
      name: DramaOnHitData
      fullName: Phoenix.Drama.DramaOnHitData
      assemblyName: HotFix
    jsonData: "{\n    \"startFrame\": 47.0,\n    \"skillEffectId\": 0,\n    \"effectType\":
      {\n        \"m_enumName\": \"Damage\",\n        \"value\": 3\n    },\n    \"damageEntityData\":
      {\n        \"moveLength\": 4.0,\n        \"damagePercent\": 20.0,\n       
      \"audioWeaponType\": {\n            \"m_enumName\": \"\",\n            \"value\":
      1\n        },\n        \"audioAttackType\": {\n            \"m_enumName\":
      \"\",\n            \"value\": 3\n        },\n        \"audioDamageLevel\":
      {\n            \"m_enumName\": \"\",\n            \"value\": 2\n        }\n   
      },\n    \"moveEntityData\": {\n        \"moveTime\": 0.0\n    },\n    \"entityExtraMoveData\":
      {},\n    \"entityExtraActionData\": {},\n    \"changeTeamEnergyData\": {},\n   
      \"changeTeamData\": {},\n    \"reduceCoolTimeData\": {}\n}"
  - typeInfo:
      name: DramaPlayEffectData
      fullName: Phoenix.Drama.DramaPlayEffectData
      assemblyName: HotFix
    jsonData: "{\n    \"startFrame\": 65.0,\n    \"attachType\": {\n        \"m_enumName\":
      \"SourceEntity\",\n        \"value\": 1\n    },\n    \"skillEffectId\": 0,\n   
      \"bindPointId\": {\n        \"m_enumName\": \"None\",\n        \"value\": 0\n   
      },\n    \"efx\": {\n        \"path\": \"Assets/Res/Entity/Actor/jianxiake/Fx/playEFx_1_Hero2000_Combat_DoubleHitRootMotion.prefab\",\n       
      \"guid\": \"5cbb13b7fa221ce4087249d8e1e53191\",\n        \"subName\": \"\"\n   
      },\n    \"isFollow\": false,\n    \"useAttachScale\": false,\n    \"waitEnd\":
      false\n}"
  - typeInfo:
      name: DramaOnHitData
      fullName: Phoenix.Drama.DramaOnHitData
      assemblyName: HotFix
    jsonData: "{\n    \"startFrame\": 68.0,\n    \"skillEffectId\": 0,\n    \"effectType\":
      {\n        \"m_enumName\": \"Damage\",\n        \"value\": 3\n    },\n    \"damageEntityData\":
      {\n        \"moveLength\": 3.0,\n        \"damagePercent\": 40.0,\n       
      \"audioWeaponType\": {\n            \"m_enumName\": \"\",\n            \"value\":
      1\n        },\n        \"audioAttackType\": {\n            \"m_enumName\":
      \"\",\n            \"value\": 2\n        },\n        \"audioDamageLevel\":
      {\n            \"m_enumName\": \"\",\n            \"value\": 3\n        }\n   
      },\n    \"moveEntityData\": {\n        \"moveTime\": 0.0\n    },\n    \"entityExtraMoveData\":
      {},\n    \"entityExtraActionData\": {},\n    \"changeTeamEnergyData\": {},\n   
      \"changeTeamData\": {},\n    \"reduceCoolTimeData\": {}\n}"
