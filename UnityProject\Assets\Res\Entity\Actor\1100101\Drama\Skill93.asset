%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 16dddab11d0a3934d85db2ba705badf7, type: 3}
  m_Name: Skill93
  m_EditorClassIdentifier: 
  adjustDistance: 0
  m_serializableDataList:
  - typeInfo:
      name: DramaPlayAnimationData
      fullName: Phoenix.Drama.DramaPlayAnimationData
      assemblyName: HotFix
    jsonData: "{\n    \"startFrame\": 0.0,\n    \"animationName\": \"CastSkill02\",\n   
      \"skillEffectId\": -1\n}"
  - typeInfo:
      name: DramaPlayEffectData
      fullName: Phoenix.Drama.DramaPlayEffectData
      assemblyName: HotFix
    jsonData: "{\n    \"startFrame\": 0.0,\n    \"attachType\": {\n        \"m_enumName\":
      \"SourceEntity\",\n        \"value\": 1\n    },\n    \"skillEffectId\": 0,\n   
      \"bindPointId\": {\n        \"m_enumName\": \"None\",\n        \"value\": 0\n   
      },\n    \"efx\": {\n        \"path\": \"Assets/Res/Entity/Actor/jianxiake/Fx/PlayEFx_0_Hero2000_CastSkill02.prefab\",\n       
      \"guid\": \"2d7f6ab00ae7da14c8862396eb56c0e9\",\n        \"subName\": \"\"\n   
      },\n    \"isFollow\": false,\n    \"useAttachScale\": false,\n    \"waitEnd\":
      false\n}"
  - typeInfo:
      name: DramaPlaySoundData
      fullName: Phoenix.Drama.DramaPlaySoundData
      assemblyName: HotFix
    jsonData: "{\n    \"startFrame\": 0.0,\n    \"name\": \"\",\n    \"wwiseEventReference\":
      {\n        \"path\": \"Assets/Res/Wwise/ScriptableObjects/Event/4DE7A6BC-EC65-4BF6-8879-5A05A0184A39.asset\",\n       
      \"guid\": \"8f15a070a2bf1484eb68640b574f2943\",\n        \"subName\": \"\"\n   
      },\n    \"skillEffectId\": -1\n}"
  - typeInfo:
      name: DramaOnHitData
      fullName: Phoenix.Drama.DramaOnHitData
      assemblyName: HotFix
    jsonData: "{\n    \"startFrame\": 42.0,\n    \"skillEffectId\": 0,\n    \"effectType\":
      {\n        \"m_enumName\": \"Damage\",\n        \"value\": 3\n    },\n    \"damageEntityData\":
      {\n        \"moveLength\": 0.0,\n        \"damagePercent\": 100.0,\n       
      \"audioWeaponType\": {\n            \"m_enumName\": \"\",\n            \"value\":
      1\n        },\n        \"audioAttackType\": {\n            \"m_enumName\":
      \"\",\n            \"value\": 3\n        },\n        \"audioDamageLevel\":
      {\n            \"m_enumName\": \"\",\n            \"value\": 3\n        }\n   
      },\n    \"moveEntityData\": {\n        \"moveTime\": 0.0\n    },\n    \"entityExtraMoveData\":
      {},\n    \"entityExtraActionData\": {},\n    \"changeTeamEnergyData\": {},\n   
      \"changeTeamData\": {},\n    \"reduceCoolTimeData\": {}\n}"
