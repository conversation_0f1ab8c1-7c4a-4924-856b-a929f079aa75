%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 16dddab11d0a3934d85db2ba705badf7, type: 3}
  m_Name: NormalAttack01
  m_EditorClassIdentifier: 
  adjustDistance: 0
  m_serializableDataList:
  - typeInfo:
      name: DramaPlayAnimationData
      fullName: Phoenix.Drama.DramaPlayAnimationData
      assemblyName: HotFix
    jsonData: "{\n    \"startFrame\": 0.0,\n    \"animationName\": \"Combat_AttackRootMotion_test01\",\n   
      \"skillEffectId\": -1\n}"
  - typeInfo:
      name: DramaPlaySoundData
      fullName: Phoenix.Drama.DramaPlaySoundData
      assemblyName: HotFix
    jsonData: "{\n    \"startFrame\": 0.0,\n    \"name\": \"\",\n    \"wwiseEventReference\":
      {\n        \"path\": \"Assets/Res/Wwise/ScriptableObjects/Event/00E18AD9-0E71-4AB7-8810-4EB9CE22DA4F.asset\",\n       
      \"guid\": \"5c4de7cc0a65f8342b0b0ff0be7adc17\",\n        \"subName\": \"\"\n   
      },\n    \"skillEffectId\": -1\n}"
  - typeInfo:
      name: DramaOnHitData
      fullName: Phoenix.Drama.DramaOnHitData
      assemblyName: HotFix
    jsonData: "{\n    \"startFrame\": 5.0,\n    \"skillEffectId\": 0,\n    \"effectType\":
      {\n        \"m_enumName\": \"Damage\",\n        \"value\": 3\n    },\n    \"damageEntityData\":
      {\n        \"moveLength\": 0.0,\n        \"damagePercent\": 100.0,\n       
      \"audioWeaponType\": {\n            \"m_enumName\": \"\",\n            \"value\":
      1\n        },\n        \"audioAttackType\": {\n            \"m_enumName\":
      \"\",\n            \"value\": 2\n        },\n        \"audioDamageLevel\":
      {\n            \"m_enumName\": \"\",\n            \"value\": 3\n        }\n   
      },\n    \"moveEntityData\": {\n        \"moveTime\": 0.0\n    },\n    \"entityExtraMoveData\":
      {},\n    \"entityExtraActionData\": {},\n    \"changeTeamEnergyData\": {},\n   
      \"changeTeamData\": {},\n    \"reduceCoolTimeData\": {}\n}"
  - typeInfo:
      name: DramaPlayEffectData
      fullName: Phoenix.Drama.DramaPlayEffectData
      assemblyName: HotFix
    jsonData: "{\n    \"startFrame\": 5.0,\n    \"attachType\": {\n        \"m_enumName\":
      \"SourceEntity\",\n        \"value\": 1\n    },\n    \"skillEffectId\": 0,\n   
      \"bindPointId\": {\n        \"m_enumName\": \"None\",\n        \"value\": 0\n   
      },\n    \"efx\": {\n        \"path\": \"Assets/Res/Entity/Actor/jianxiake/Fx/PlayEFx_0_Hero2000_Combat_AttackRootMotion.prefab\",\n       
      \"guid\": \"374da7f46c0a4344c853ff4f0531bd16\",\n        \"subName\": \"\"\n   
      },\n    \"isFollow\": false,\n    \"useAttachScale\": false,\n    \"waitEnd\":
      false\n}"
